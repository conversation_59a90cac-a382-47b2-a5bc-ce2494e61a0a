<!-- 消息列表组件 -->
<template>
  <div ref="messagesArea" class="flex-1 overflow-y-auto p-4 custom-scrollbar">
    <!-- 未选择联系人时的占位符 -->
    <div
      v-if="!currentContact"
      class="flex flex-col items-center justify-center h-full text-gray-500"
    >
      <div class="w-40 h-40 mb-6">
        <img src="../assets/editor/chat-placeholder.svg" alt="选择聊天" class="w-full h-full" />
      </div>
      <p class="text-sm font-medium">请选择一个聊天开始对话</p>
    </div>
    <!-- 已选择联系人但正在加载消息 -->
    <div
      v-else-if="!messages.length && isCurrentContactLoading"
      class="flex flex-col items-center justify-center h-full text-gray-500"
    >
      <div class="w-12 h-12 mb-4">
        <div
          class="w-full h-full border-4 border-blue-500 border-t-transparent rounded-full animate-spin"
        ></div>
      </div>
      <p class="text-sm font-medium mb-1">正在加载聊天记录</p>
      <p class="text-xs text-gray-400">请稍候...</p>
    </div>
    <!-- 已选择联系人但无消息 -->
    <div
      v-else-if="!messages.length"
      class="flex items-center justify-center h-full text-gray-500 text-sm"
    >
      暂无消息
    </div>
    <div v-else>
      <div v-for="message in messages" :key="message.id" class="mb-4">
        <!-- 判断是否为当前用户发送的消息 -->
        <div v-if="isCurrentUserMessage(message)" class="flex items-start gap-3 justify-end">
          <!-- 重试按钮，在发送失败时显示 -->
          <div v-if="message.sendError" class="flex items-center">
            <button
              @click="retryMessage(message)"
              class="w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-colors duration-200"
              title="重新发送"
            >
              <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fill-rule="evenodd"
                  d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                  clip-rule="evenodd"
                />
              </svg>
            </button>
          </div>

          <!-- 当前用户消息：内容在左，头像在右 -->
          <div class="flex-1 flex flex-col items-end">
            <div class="flex items-center gap-2 mb-2 text-right">
              <!-- 发送状态指示 -->
              <div v-if="message.isSending" class="flex items-center gap-1">
                <div
                  class="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin"
                ></div>
                <span class="text-xs text-gray-400">发送中...</span>
              </div>
              <div v-else-if="message.sendError" class="flex items-center gap-1">
                <svg class="w-3 h-3 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fill-rule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                    clip-rule="evenodd"
                  />
                </svg>
                <span class="text-xs text-red-500">发送失败</span>
              </div>
              <span class="text-xs text-gray-400 font-normal">
                {{ formatMessageTime(message.timestamp) }}
              </span>
              <span class="text-xs text-gray-600 font-medium">{{ message.senderName }}</span>
            </div>
            <div
              :class="[
                'rounded-lg p-3 max-w-md shadow-sm',
                message.sendError ? 'bg-red-50 border border-red-200' : 'bg-[#c9daf5] text-white'
              ]"
            >
              <div
                :class="[
                  'text-sm leading-relaxed break-words whitespace-pre-wrap',
                  message.sendError ? 'text-red-700' : 'text-gray-900'
                ]"
                v-text="message.content"
              ></div>
            </div>
          </div>
          <UserDetailPopover
            :user-id="getCurrentUserId(message)"
            v-model:open="popoverStates[message.id + '_current']"
          >
            <UserAvatar
              :name="message.senderName"
              size="medium"
              class="cursor-pointer hover:opacity-80 transition-opacity"
            />
          </UserDetailPopover>
        </div>

        <!-- 其他用户消息：头像在左，内容在右 -->
        <div v-else class="flex items-start gap-3">
          <UserDetailPopover :user-id="message.senderId" v-model:open="popoverStates[message.id]">
            <UserAvatar
              :name="message.senderName"
              size="medium"
              class="cursor-pointer hover:opacity-80 transition-opacity"
            />
          </UserDetailPopover>
          <div class="flex flex-col">
            <div class="flex items-center gap-2 mb-2">
              <span class="text-xs text-gray-600 font-medium">{{ message.senderName }}</span>
              <span class="text-xs text-gray-400 font-normal">
                {{ formatMessageTime(message.timestamp) }}
              </span>
            </div>
            <div class="bg-[#ffffff] border border-gray-200 rounded-lg p-3 shadow-sm max-w-md">
              <div
                class="text-sm text-gray-900 leading-relaxed break-words whitespace-pre-wrap"
                v-text="message.content"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch, reactive, onMounted, onUnmounted, computed } from 'vue'
import UserAvatar from './UserAvatar.vue'
import UserDetailPopover from './UserDetailPopover.vue'
import { useUserStore } from '../store/user'
import { useMessageStore } from '../store/message/index'
import { notificationService } from '../services/notificationService'
import { formatMessageTime } from '../utils/time-utils'

import type { User } from '../api'

interface Message {
  id: string
  senderId: string
  senderName: string
  content: string
  timestamp: Date
  isSending?: boolean
  sendError?: string
}

interface Contact {
  id: string
  name: string
  avatar: string
  status: string
  lastMessage: string
  lastMessageTime: Date
  user: User
  hasLastMessage?: boolean
}

interface Props {
  messages: Message[]
  currentContact: Contact | null
}

const props = defineProps<Props>()

// 用户状态管理
const userStore = useUserStore()
const messageStore = useMessageStore()

const messagesArea = ref<HTMLElement>()

// 管理每个消息的 Popover 状态
const popoverStates = reactive<Record<string, boolean>>({})

// 判断是否为当前用户发送的消息
const isCurrentUserMessage = (message: Message) => {
  const currentUserId = userStore.currentUser.value?.id
  return message.senderId === currentUserId
}

// 检测当前联系人是否正在加载消息历史
const isCurrentContactLoading = computed(() => {
  if (!props.currentContact) return false
  return messageStore.isUserLoading(props.currentContact.id)
})

// 监听加载状态变化，用于调试
watch(isCurrentContactLoading, (isLoading) => {
  if (props.currentContact) {
    console.log(`🔍 [MessageList] ${props.currentContact.name} 加载状态: ${isLoading}`)
  }
})

// 获取当前用户的真实 ID
const getCurrentUserId = (message: Message) => {
  if (isCurrentUserMessage(message)) {
    return userStore.currentUser?.id || null
  }
  return message.senderId
}

// 初始化 Popover 状态
watch(
  () => props.messages,
  (newMessages) => {
    newMessages.forEach((message) => {
      // 为其他用户消息创建 Popover 状态
      if (!(message.id in popoverStates)) {
        popoverStates[message.id] = false
      }
      // 为当前用户消息创建 Popover 状态
      const currentUserKey = message.id + '_current'
      if (!(currentUserKey in popoverStates)) {
        popoverStates[currentUserKey] = false
      }
    })
  },
  { immediate: true, deep: true }
)

// 滚动到底部
const scrollToBottom = () => {
  if (messagesArea.value) {
    messagesArea.value.scrollTop = messagesArea.value.scrollHeight
  }
}

// 监听消息变化，自动滚动到底部
watch(
  () => props.messages,
  () => {
    nextTick(() => {
      scrollToBottom()
    })
  },
  { deep: true }
)

// 添加 ResizeObserver 来监听容器高度变化
let resizeObserver: ResizeObserver | null = null

onMounted(() => {
  // 监听容器高度变化
  if (messagesArea.value) {
    resizeObserver = new ResizeObserver(() => {
      nextTick(() => {
        scrollToBottom()
      })
    })
    resizeObserver.observe(messagesArea.value)
  }
})

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
})

// 重试发送失败的消息
const retryMessage = async (message: Message) => {
  if (!props.currentContact || !message.sendError) {
    return
  }

  try {
    const success = await messageStore.retryMessage(props.currentContact.id, message.id)
    if (success) {
      notificationService.success('消息重发成功')
    } else {
      notificationService.error('消息重发失败，请稍后重试')
    }
  } catch (error) {
    console.error('重试消息失败:', error)
    notificationService.error('消息重发失败')
  }
}

// 暴露滚动方法给父组件
defineExpose({
  scrollToBottom
})
</script>

<style scoped>
/* 自定义滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1; /* 滚动条轨道背景色 */
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1; /* 滚动条滑块背景色 */
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8; /* 滚动条滑块悬停背景色 */
}
</style>
